{"strength_classes": [{"class": "B15", "description": "Класс прочности B15 (М200) - для садовых дорожек и декоративных элементов"}, {"class": "B20", "description": "Класс прочности B20 (М250) - для пешеходных зон с малой нагрузкой"}, {"class": "B22.5", "description": "Класс прочности B22.5 (М300) - для пешеходных зон со средней нагрузкой"}, {"class": "B25", "description": "Класс прочности B25 (М350) - для пешеходных зон с высокой нагрузкой"}, {"class": "B30", "description": "Класс прочности B30 (М400) - для проезжей части с легковым транспортом"}, {"class": "B35", "description": "Класс прочности B35 (М450) - для проезжей части с грузовым транспортом"}, {"class": "B40", "description": "Класс прочности B40 (М550) - для промышленных зон с высокой нагрузкой"}], "frost_resistance": [{"class": "F50", "description": "Морозостойкость F50 - до 50 циклов замораживания-оттаивания"}, {"class": "F100", "description": "Морозостойкость F100 - до 100 циклов замораживания-оттаивания"}, {"class": "F150", "description": "Морозостойкость F150 - до 150 циклов замораживания-оттаивания"}, {"class": "F200", "description": "Морозостойкость F200 - до 200 циклов замораживания-оттаивания"}, {"class": "F300", "description": "Морозостойкость F300 - до 300 циклов замораживания-оттаивания"}], "water_absorption": [{"class": "W2", "description": "Водопоглощение до 2%"}, {"class": "W4", "description": "Водопоглощение до 4%"}, {"class": "W6", "description": "Водопоглощение до 6%"}, {"class": "W8", "description": "Водопоглощение до 8%"}], "sizes": {"pavers": [{"length": 200, "width": 100, "height": 40}, {"length": 200, "width": 100, "height": 60}, {"length": 200, "width": 100, "height": 80}, {"length": 300, "width": 300, "height": 30}, {"length": 300, "width": 300, "height": 40}, {"length": 300, "width": 300, "height": 60}], "curbs": [{"length": 500, "width": 50, "height": 200}, {"length": 1000, "width": 80, "height": 200}, {"length": 1000, "width": 150, "height": 300}, {"length": 1000, "width": 180, "height": 300}], "drainage": [{"length": 1000, "width": 140, "height": 60}, {"length": 1000, "width": 160, "height": 70}, {"length": 1000, "width": 200, "height": 85}]}, "surfaces": [{"id": "flat", "name": "одноуровневая", "description": "Плоская поверхность одного уровня без перепадов высоты"}, {"id": "multilevel", "name": "разноуровневая", "description": "Поверхность с перепадами высоты и разными уровнями"}, {"id": "wavy", "name": "волнистая", "description": "Поверхность с плавными волнообразными переходами"}, {"id": "structured", "name": "структурированная", "description": "Поверхность с выраженной структурой и рельефом"}, {"id": "brushed", "name": "бра<PERSON><PERSON>рованная", "description": "Поверхность с эффектом браширования (искусственного состаривания)"}, {"id": "polished", "name": "полированная", "description": "Гладкая отполированная поверхность с блеском"}, {"id": "matte", "name": "матовая", "description": "Гладкая поверхность без блеска с матовым эффектом"}], "color_pigments": [{"id": "no_pigment", "name": "без красителя (серый)", "description": "Изделие без добавления цветных пигментов, стандартный серый цвет."}, {"id": "one_pigment", "name": "1 краситель", "description": "Изделие с добавлением одного пигмента для получения однотонного цвета."}, {"id": "two_pigments", "name": "2 красителя", "description": "Изделие с добавлением двух пигментов для получения сложного цвета или оттенка."}, {"id": "three_or_more_pigments", "name": "3 и более красителей", "description": "Изделие с добавлением трёх и более пигментов для создания многоцветных или сложных цветовых решений."}], "textures": ["шероховатая", "рельефная", "состаренная", "полированная", "колотая", "фактурная", "имитация камня", "имитация дерева", "имитация кирпича", "мрамор", "гладкая"], "colors": [{"id": "gray", "name": "Серый", "hex": "#808080"}, {"id": "red", "name": "Красный", "hex": "#B22222"}, {"id": "brown", "name": "Коричневый", "hex": "#8B4513"}, {"id": "yellow", "name": "Желт<PERSON>й", "hex": "#FFD700"}, {"id": "black", "name": "Черный", "hex": "#000000"}, {"id": "white", "name": "Белый", "hex": "#FFFFFF"}, {"id": "sand", "name": "Песочный", "hex": "#F5DEB3"}, {"id": "terracotta", "name": "Терракотовый", "hex": "#E2725B"}, {"id": "green", "name": "Зеленый", "hex": "#228B22"}, {"id": "blue", "name": "Синий", "hex": "#4169e1"}, {"id": "pink", "name": "Розовый", "hex": "#eb6fa5"}], "patterns": [{"id": "none", "name": "нет рисунка", "description": "Плитка без рисунка с однородной поверхностью"}, {"id": "parquet", "name": "паркет", "description": "Имитация паркетной укладки с прямоугольными элементами"}, {"id": "brick", "name": "<PERSON>и<PERSON><PERSON><PERSON><PERSON>", "description": "Имитация кирпичной кладки со смещением"}, {"id": "herringbone", "name": "елочка", "description": "Рисунок в виде елочки из прямоугольных элементов"}, {"id": "cobblestone", "name": "булыжник", "description": "Имитация старинной булыжной мостовой с неровными краями"}, {"id": "mosaic", "name": "мозаика", "description": "Мозаичный рисунок из мелких элементов разной формы"}, {"id": "wave", "name": "волна", "description": "Волнообразный рисунок на поверхности плитки"}, {"id": "geometric", "name": "геометрический", "description": "Геометрические узоры на поверхности плитки"}, {"id": "floral", "name": "цветочный", "description": "Растительные и цветочные орнаменты"}, {"id": "antique", "name": "античный", "description": "Имитация античной кладки с состаренной поверхностью"}], "weight": [{"value": 0.5, "unit": "кг"}, {"value": 1, "unit": "кг"}, {"value": 2.5, "unit": "кг"}, {"value": 5, "unit": "кг"}, {"value": 8.5, "unit": "кг"}, {"value": 10, "unit": "кг"}, {"value": 15, "unit": "кг"}, {"value": 20, "unit": "кг"}, {"value": 500, "unit": "г"}, {"value": 750, "unit": "г"}, {"value": 1000, "unit": "г"}], "material": [{"id": "stal", "name": "Сталь"}, {"id": "aluminium", "name": "Алюминий", "description": "Легкий и крепкий материал"}, {"id": "derevo", "name": "Дерево", "description": "Дерево деревянное"}, {"id": "kozha", "name": "Кожа", "description": "Натура<PERSON>ьна кожа"}], "tip_pokrytiya": ["Пар<PERSON><PERSON>т", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "razmer_upakovki": ["260 х 500 х 60"]}