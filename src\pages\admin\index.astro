---
import AdminLayout from '../../layouts/AdminLayout.astro';
import { isAuthenticated } from '../../utils/auth';
import Card from '../../components/ui/Card.astro';
import CardHeader from '../../components/ui/CardHeader.astro';
import CardTitle from '../../components/ui/CardTitle.astro';
import CardDescription from '../../components/ui/CardDescription.astro';
import CardContent from '../../components/ui/CardContent.astro';
import Badge from '../../components/ui/Badge.astro';
import StatsWithActions from '../../components/admin/StatsWithActions.astro';
import ProductCard from '../../components/admin/ProductCard.astro';

// Простая защита админ-панели
if (!isAuthenticated(Astro.request)) {
  return Astro.redirect('/admin/login');
}

// Загрузка данных о товарах, категориях и атрибутах
import productsData from '../../../data/product/products.json';
import categoriesData from '../../../data/product/categories.json';
import attributesData from '../../../data/product/attributes.json';

// Подсчет статистики
const totalProducts = productsData.length;
const totalCategories = categoriesData.categories.length;
const inStockProducts = productsData.filter(product => product.inStock).length;
const outOfStockProducts = totalProducts - inStockProducts;

// Подсчет типов атрибутов
const attributeTypes = {
  colors: 'Цвет',
  textures: 'Текстура',
  strength_classes: 'Класс прочности',
  frost_resistance: 'Морозостойкость',
  water_absorption: 'Водопоглощение',
  standard_sizes: 'Размер',
  surfaces: 'Поверхность',
  patterns: 'Рисунок',
  color_pigments: 'Цветовые пигменты'
};
const totalAttributeTypes = Object.keys(attributeTypes).length;

// Статистика по категориям
const categoryStats = categoriesData.categories.map(category => {
  const categoryProducts = productsData.filter(product => product.categorySlug === category.slug);
  return {
    name: category.name,
    count: categoryProducts.length,
    slug: category.slug
  };
});

// Популярные товары
const popularProducts = productsData
  .sort((a, b) => b.popularity - a.popularity)
  .slice(0, 5);

// Данные для объединенных блоков статистики с действиями
const statsWithActionsData = [
  {
    title: 'Товары',
    value: totalProducts,
    description: 'Общее количество в каталоге',
    icon: '<svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M20.25 7.5l-.625 10.632a2.25 2.25 0 01-2.247 2.118H6.622a2.25 2.25 0 01-2.247-2.118L3.75 7.5M10 11.25h4M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125z" /></svg>',
    iconColor: 'bg-blue-500',
    action: {
      title: 'Добавить товар',
      href: '/admin/products/new',
      icon: '<svg class="h-4 w-4 text-gray-600" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" /></svg>'
    }
  },
  {
    title: 'В каталоге',
    value: inStockProducts,
    description: 'Активные товары',
    icon: '<svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>',
    iconColor: 'bg-green-500',
    action: {
      title: 'Управление товарами',
      href: '/admin/products',
      icon: '<svg class="h-4 w-4 text-gray-600" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M10.5 6h9.75M10.5 6a1.5 1.5 0 11-3 0m3 0a1.5 1.5 0 10-3 0M3.75 6H7.5m0 12h9.75m-9.75 0a1.5 1.5 0 01-3 0m3 0a1.5 1.5 0 00-3 0m-3.75 0H7.5m9-6h3.75m-3.75 0a1.5 1.5 0 01-3 0m3 0a1.5 1.5 0 00-3 0m-9.75 0h3.75" /></svg>'
    }
  },
  {
    title: 'Категории',
    value: totalCategories,
    description: 'Основные разделы',
    icon: '<svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M2.25 7.125C2.25 6.504 2.754 6 3.375 6h6c.621 0 1.125.504 1.125 1.125v3.75c0 .621-.504 1.125-1.125 1.125h-6a1.125 1.125 0 01-1.125-1.125v-3.75zM14.25 8.625c0-.621.504-1.125 1.125-1.125h5.25c.621 0 1.125.504 1.125 1.125v8.25c0 .621-.504 1.125-1.125 1.125h-5.25a1.125 1.125 0 01-1.125-1.125v-8.25zM3.75 16.125c0-.621.504-1.125 1.125-1.125h5.25c.621 0 1.125.504 1.125 1.125v2.25c0 .621-.504 1.125-1.125 1.125h-5.25a1.125 1.125 0 01-1.125-1.125v-2.25z" /></svg>',
    iconColor: 'bg-purple-500',
    action: {
      title: 'Управление категориями',
      href: '/admin/categories',
      icon: '<svg class="h-4 w-4 text-gray-600" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M10.5 6h9.75M10.5 6a1.5 1.5 0 11-3 0m3 0a1.5 1.5 0 10-3 0M3.75 6H7.5m0 12h9.75m-9.75 0a1.5 1.5 0 01-3 0m3 0a1.5 1.5 0 00-3 0m-3.75 0H7.5m9-6h3.75m-3.75 0a1.5 1.5 0 01-3 0m3 0a1.5 1.5 0 00-3 0m-9.75 0h3.75" /></svg>'
    }
  },
  {
    title: 'Атрибуты',
    value: totalAttributeTypes,
    description: 'Типы характеристик',
    icon: '<svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M9.568 3H5.25A2.25 2.25 0 003 5.25v4.318c0 .597.237 1.17.659 1.591l9.581 9.581c.699.699 1.78.872 2.607.33a18.095 18.095 0 005.223-5.223c.542-.827.369-1.908-.33-2.607L11.16 3.66A2.25 2.25 0 009.568 3z" /></svg>',
    iconColor: 'bg-orange-500',
    action: {
      title: 'Управление атрибутами',
      href: '/admin/attributes',
      icon: '<svg class="h-4 w-4 text-gray-600" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M10.5 6h9.75M10.5 6a1.5 1.5 0 11-3 0m3 0a1.5 1.5 0 10-3 0M3.75 6H7.5m0 12h9.75m-9.75 0a1.5 1.5 0 01-3 0m3 0a1.5 1.5 0 00-3 0m-3.75 0H7.5m9-6h3.75m-3.75 0a1.5 1.5 0 01-3 0m3 0a1.5 1.5 0 00-3 0m-9.75 0h3.75" /></svg>'
    }
  }
];


---

<AdminLayout title="Админ-панель | LuxBeton">
  <div class="space-y-8">
    <!-- Заголовок с приветствием -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between animate-fade-in">
      <div>
        <h1 class="text-3xl font-bold tracking-tight text-gray-900">Панель управления</h1>
        <p class="mt-2 text-sm text-gray-600">Добро пожаловать в админ-панель LuxBeton</p>
      </div>
      <div class="mt-4 sm:mt-0">
        <span class="inline-flex items-center rounded-md bg-green-50 px-2 py-1 text-xs font-medium text-green-700 ring-1 ring-inset ring-green-600/20 animate-pulse">
          <div class="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
          Система работает
        </span>
      </div>
    </div>

    <!-- Объединенные блоки статистики с действиями -->
    <div class="animate-slide-in" style="animation-delay: 0.1s;">
      <StatsWithActions stats={statsWithActionsData} />
    </div>

    <!-- Дополнительная информация -->
    <div class="grid grid-cols-1 gap-6 lg:grid-cols-2 animate-slide-in" style="animation-delay: 0.2s;">
      <!-- Пустой блок для будущих дополнений -->

    <!-- Популярные товары -->
    <div class="animate-slide-in" style="animation-delay: 0.4s;">
      <Card class="hover-lift card-transition">
      <CardHeader>
        <CardTitle>Популярные товары</CardTitle>
        <CardDescription>Топ-5 товаров по рейтингу популярности</CardDescription>
      </CardHeader>
      <CardContent>
        <!-- Desktop Table -->
        <div class="hidden md:block overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead>
              <tr class="bg-gray-50">
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Товар</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Категория</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Рейтинг</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Цена</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Статус</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              {popularProducts.map(product => (
                <tr class="hover:bg-gray-50 transition-colors">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="h-10 w-10 flex-shrink-0">
                        <div class="h-10 w-10 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                          <svg class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z" />
                          </svg>
                        </div>
                      </div>
                      <div class="ml-4">
                        <div class="text-sm font-medium text-gray-900">{product.name}</div>
                        <div class="text-sm text-gray-500">{product.id}</div>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <Badge variant="outline">{product.category}</Badge>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <svg class="h-4 w-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                      </svg>
                      <span class="ml-1 text-sm font-medium text-gray-900">{product.popularity}</span>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-medium">
                    {product.basePrice.value} ₽/{product.basePrice.unit}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <Badge variant={product.inStock ? 'success' : 'destructive'}>
                      {product.inStock ? 'В наличии' : 'Нет в наличии'}
                    </Badge>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        <!-- Mobile Cards -->
        <div class="md:hidden space-y-4">
          {popularProducts.map(product => (
            <ProductCard product={product} />
          ))}
        </div>
      </CardContent>
      </Card>
    </div>
  </div>
</AdminLayout>
