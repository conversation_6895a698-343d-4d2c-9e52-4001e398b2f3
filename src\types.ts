export interface Product {
  id: string;
  name: string;
  slug: string;
  category: string;
  categorySlug: string;
  subcategory: string;
  shortDescription: string;
  fullDescription: string;
  basePrice: {
    value: number;
    unit: string;
  };
  attributes: {
    colors: string[];
    color_pigments?: {
      id: string;
      name: string;
      description: string;
    };
    texture: string;
    size: {
      length?: number;
      width?: number;
      height?: number;
      variants?: Array<{
        length: number;
        width: number;
        height: number;
      }>;
    };
    weight: number;
    strength: string;
    surface?: string;
    pattern?: string;
  };
  images: {
    main: string;
    additional: string[];
  };
  inStock: boolean;
  popularity: number;
  rating?: number;
  reviews?: number;
  createdAt?: string;
  updatedAt?: string;
}
