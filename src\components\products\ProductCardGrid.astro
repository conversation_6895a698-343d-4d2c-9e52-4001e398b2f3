---
import type { Product } from '../../types';
import Tooltip from '../ui/Tooltip.astro';

interface Props {
  product: Product;
  attributeTypesConfig?: any;
  'data-size'?: string;
  'data-color'?: string;
  'data-price'?: string | number;
  'data-name'?: string;
}

const { product, attributeTypesConfig = {} } = Astro.props;

// Prepare size and color display
const mainSizeValue = product.attributes?.size?.length
  ? `${product.attributes.size.length} x ${product.attributes.size.width} x ${product.attributes.size.height} мм`
  : product.attributes?.size?.variants?.[0]
    ? `${product.attributes.size.variants[0].length} x ${product.attributes.size.variants[0].width} x ${product.attributes.size.variants[0].height} мм`
    : null;

const mainColor = product.attributes?.colors?.[0] || null;
const colorPigments = product.attributes?.color_pigments;

// Prepare color display with pigments and actual colors
const getColorDisplay = () => {
  if (!colorPigments) return mainColor;

  const colors = product.attributes?.colors || [];
  if (colors.length === 0) return colorPigments.name;

  // Determine how many colors to show based on pigment type
  let colorsToShow: string[] = [];
  if (colorPigments.id === 'no_pigment') {
    colorsToShow = [colors[0]]; // Just show the main color (usually gray)
  } else if (colorPigments.id === 'one_pigment') {
    colorsToShow = colors.slice(0, 1); // Show 1 color
  } else if (colorPigments.id === 'two_pigments') {
    colorsToShow = colors.slice(0, 2); // Show 2 colors
  } else if (colorPigments.id === 'three_or_more_pigments') {
    colorsToShow = colors.slice(0, 3); // Show 3 colors
  }

  if (colorsToShow.length === 0) {
    return colorPigments.name;
  }

  return `${colorPigments.name} (${colorsToShow.join(', ')})`;
};

const colorDisplay = getColorDisplay();

// Функция для получения отображаемого значения атрибута
const getAttributeDisplayValue = (attributeType: string, attributeValue: any) => {
  if (!attributeValue) return null;

  switch (attributeType) {
    case 'weight':
      if (typeof attributeValue === 'object' && attributeValue.value && attributeValue.unit) {
        return `${attributeValue.value} ${attributeValue.unit}`;
      }
      if (typeof attributeValue === 'number') {
        return `${attributeValue} кг`;
      }
      break;

    case 'strength_classes':
      if (typeof attributeValue === 'object' && attributeValue.class) {
        return attributeValue.class;
      }
      break;

    case 'water_absorption':
    case 'frost_resistance':
      if (typeof attributeValue === 'object' && attributeValue.class) {
        return attributeValue.class;
      }
      break;

    case 'textures':
      if (Array.isArray(attributeValue)) {
        return attributeValue.join(', ');
      }
      if (typeof attributeValue === 'string') {
        return attributeValue;
      }
      break;

    case 'surfaces':
    case 'patterns':
    case 'material':
    case 'tip_pokrytiya':
      // Для объектов с полями id, name, description
      if (typeof attributeValue === 'object' && attributeValue.name) {
        return attributeValue.name;
      }
      if (Array.isArray(attributeValue)) {
        return attributeValue.map(item =>
          typeof item === 'object' && item.name ? item.name : item
        ).join(', ');
      }
      if (typeof attributeValue === 'string') {
        return attributeValue;
      }
      break;

    default:
      // Для простых строковых атрибутов
      if (typeof attributeValue === 'string' && attributeValue.trim()) {
        return attributeValue;
      }
      if (Array.isArray(attributeValue)) {
        return attributeValue.join(', ');
      }
      // Для объектов с полем name (универсальная обработка)
      if (typeof attributeValue === 'object' && attributeValue.name) {
        return attributeValue.name;
      }
      break;
  }

  return null;
};

// Функция для получения названия атрибута
const getAttributeName = (attributeType: string) => {
  const names: Record<string, string> = {
    'textures': 'Текстура',
    'strength_classes': 'Класс прочности',
    'frost_resistance': 'Морозостойкость',
    'water_absorption': 'Водопоглощение',
    'surfaces': 'Поверхность',
    'patterns': 'Рисунок',
    'weight': 'Вес',
    'material': 'Материал',
    'tip_pokrytiya': 'Тип покрытия'
  };
  return names[attributeType] || attributeType;
};

// Получаем все атрибуты для отображения (исключая специально обработанные)
const displayAttributes = Object.entries(product.attributes || {})
  .filter(([key]) => !['size', 'colors', 'color_pigments'].includes(key))
  .map(([key, value]) => {
    // Проверяем настройку отображения в карточке товара
    const config = attributeTypesConfig[key];

    if (config?.showInProductCard !== true) {
      return null;
    }

    const displayValue = getAttributeDisplayValue(key, value);
    if (!displayValue) return null;

    return {
      key,
      name: getAttributeName(key),
      value: displayValue
    };
  })
  .filter(attr => attr !== null);

---

<div class="product-card bg-white shadow-md rounded-none overflow-visible group flex flex-col"
     data-category={product.category}
     data-size={Astro.props['data-size']}
     data-color={Astro.props['data-color']}
     data-price={Astro.props['data-price']}
     data-name={Astro.props['data-name']}
     data-subcategory={product.subcategory.toLowerCase().replace(/\s+/g, '-')}>
  {/* Image Section (clickable) */}
  <a href={`/products/${product.categorySlug}/${product.slug}`} class="relative overflow-hidden flex-shrink-0 w-full aspect-square block">
    <img
      src={`/product/${product.images.main}`}
      alt={product.name}
      class="w-full h-full object-cover transform group-hover:scale-105 transition-transform duration-300"
    />
  </a>

  {/* Details Section (Grid View) */}
  <div class="p-2 sm:p-3 lg:p-3 xl:p-3 2xl:p-4 flex-grow flex flex-col">
    {/* Top row: Name */}
    <div class="mb-1">
      <h3 class="text-base lg:text-base xl:text-base 2xl:text-lg font-bold text-text-main line-clamp-2">
        <a href={`/products/${product.categorySlug}/${product.slug}`} class="hover:underline">{product.name}</a>
      </h3>
    </div>

    {/* Article */}
    <p class="text-gray-600 text-xs lg:text-xs xl:text-[12px] 2xl:text-sm mt-1 mb-1">Арт: {product.id}</p>

    {/* Size */}
    {mainSizeValue && attributeTypesConfig.sizes?.showInProductCard === true && (
      <p class="text-gray-700 text-xs lg:text-xs xl:text-[13px] 2xl:text-sm mt-1 mb-1">
        <span class="font-semibold">Размер:</span> {mainSizeValue}
      </p>
    )}

    {/* Color */}
    {(mainColor || colorPigments) && (
      (!colorPigments && mainColor && attributeTypesConfig.colors?.showInProductCard === true) ||
      (colorPigments && attributeTypesConfig.color_pigments?.showInProductCard === true)
    ) && (
      <p class="text-gray-700 text-xs lg:text-xs xl:text-[13px] 2xl:text-sm mt-1 mb-1">
        <span class="font-semibold">Цвет:</span> {colorDisplay}
        {colorPigments && (
          <Tooltip
            content={colorPigments.description}
            position="top"
            size="sm"
            triggerClass="ml-1"
          />
        )}
      </p>
    )}

    {/* Other Attributes */}
    {displayAttributes.map((attr) => (
      <p class="text-gray-700 text-xs lg:text-xs xl:text-[13px] 2xl:text-sm mt-1 mb-1">
        <span class="font-semibold">{attr.name}:</span> {attr.value}
      </p>
    ))}

    {/* Price */}
    <p class="text-[#baa385] text-base lg:text-base xl:text-lg 2xl:text-xl font-semibold mb-2 lg:mb-2 mt-auto text-right">
      {product.basePrice.value.toFixed(2)} руб. / {product.basePrice.unit}
    </p>

    {/* Buttons */}
    <div class="flex space-x-1 lg:space-x-1 xl:space-x-2 2xl:space-x-2 mt-2 lg:mt-2">
      <a href={`/products/${product.categorySlug}/${product.slug}`} class="flex-1 border border-[#baa385] bg-white text-[#baa385] font-semibold py-2.5 sm:py-2.5 lg:py-2 xl:py-2 2xl:py-2.5 text-xs lg:text-xs xl:text-sm 2xl:text-sm text-center hover:bg-gray-50 transition-colors">Подробнее</a>
      <a href="/request" class="flex-1 bg-[#baa385] text-white font-semibold py-2.5 sm:py-2.5 lg:py-2 xl:py-2 2xl:py-2.5 text-xs lg:text-xs xl:text-sm 2xl:text-sm text-center hover:bg-[#a89274] transition-colors">Заказать</a>
    </div>
  </div>
</div>
