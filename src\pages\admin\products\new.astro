---
import AdminLayout from '../../../layouts/AdminLayout.astro';
import { isAuthenticated } from '../../../utils/auth';

// Защита страницы
if (!isAuthenticated(Astro.request)) {
  return Astro.redirect('/admin/login');
}

// Загрузка данных о категориях для выпадающего списка
import categoriesFile from '../../../../data/product/categories.json';
// Фильтруем только активные категории (activeForProducts = true)
const categoriesData = categoriesFile.categories.filter(cat => cat.activeForProducts !== false);

// Загрузка данных об атрибутах
import attributesFile from '../../../../data/product/attributes.json';
import attributeTypesConfig from '../../../../data/product/attribute-types-config.json';
const attributesData = attributesFile;
const attributeTypes = attributeTypesConfig;

// Загрузка настроек товаров
import settingsData from '../../../../data/product/settings-product.json';
---

<AdminLayout title="Добавить товар | LuxBeton">
  <div class="container mx-auto py-8 px-4">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-3xl font-bold">Добавить новый товар</h1>
      <a href="/admin/products" class="bg-gray-300 text-gray-800 px-4 py-2 rounded hover:bg-gray-400">
        Назад к списку
      </a>
    </div>

    <form id="product-form" class="bg-white rounded-lg shadow-md p-6">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Основная информация -->
        <div>
          <h2 class="text-xl font-semibold mb-4">Основная информация</h2>

          <div class="mb-4">
            <label for="id" class="block text-sm font-medium text-gray-700 mb-1">ID товара</label>
            <div class="flex items-center space-x-2">
              <input
                type="text"
                id="id"
                placeholder="Будет сгенерирован автоматически"
                class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 bg-gray-50"
                readonly
                required
              />
              <button
                type="button"
                id="generate-id-btn"
                class="generate-id-btn px-3 py-2 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                style="background-color: #3b82f6;"
                disabled
              >
                Генерировать
              </button>
            </div>
            <p class="text-xs text-gray-500 mt-1">ID будет автоматически сгенерирован при выборе категории</p>
          </div>

          <div class="mb-4">
            <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Название товара</label>
            <input
              type="text"
              id="name"
              placeholder="Введите название товара"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              required
            />
          </div>

          <div class="mb-4">
            <label for="category" class="block text-sm font-medium text-gray-700 mb-1">Категория</label>
            <select
              id="category"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              required
            >
              <option value="">Выберите категорию</option>
              {categoriesData.map(cat => (
                <option value={cat.name} data-category-id={cat.id}>{cat.name}</option>
              ))}
            </select>
            <div id="category-info" class="text-xs text-gray-500 mt-1 hidden">
              <span id="category-id-prefix" class="font-medium"></span>
              <span> - префикс для ID товаров этой категории</span>
            </div>
          </div>

          <div class="mb-4">
            <label for="subcategory" class="block text-sm font-medium text-gray-700 mb-1">Подкатегория</label>
            <select
              id="subcategory"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              disabled
            >
              <option value="">Сначала выберите категорию</option>
            </select>
          </div>

          <div class="mb-4">
            <label for="product-slug" class="block text-sm font-medium text-gray-700 mb-1">SLUG (URL товара)</label>
            <div class="flex items-center space-x-2">
              <input
                type="text"
                id="product-slug"
                placeholder="url-slug-tovara"
                class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 font-mono text-sm"
              />
              <button
                type="button"
                id="generate-product-slug-btn"
                class="generate-product-slug-btn px-3 py-2 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm whitespace-nowrap"
                style="background-color: #3b82f6;"
                title="Генерировать SLUG из названия товара с транслитерацией"
              >
                Генерировать
              </button>
            </div>
            <p class="mt-1 text-xs text-gray-500">Автоматически генерируется из названия или нажмите кнопку для ручной генерации</p>
          </div>

          <div class="mb-4">
            <label for="shortDescription" class="block text-sm font-medium text-gray-700 mb-1">Краткое описание</label>
            <textarea
              id="shortDescription"
              placeholder="Краткое описание товара"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              rows="2"
              required
            ></textarea>
          </div>

          <div class="mb-4">
            <label for="fullDescription" class="block text-sm font-medium text-gray-700 mb-1">Полное описание</label>
            <textarea
              id="fullDescription"
              placeholder="Подробное описание товара"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              rows="5"
            ></textarea>
          </div>
        </div>

        <!-- Цена и атрибуты -->
        <div>
          <h2 class="text-xl font-semibold mb-4">Цена и атрибуты</h2>

          <!-- Тип товара -->
          <div class="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <label for="product-type" class="block text-sm font-medium text-gray-700 mb-1">
              <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
              </svg>
              Тип товара
            </label>
            <select
              id="product-type"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 bg-white"
              required
            >
              <!-- Динамически заполняется JavaScript -->
            </select>
            <p class="text-xs text-blue-600 mt-1">
              <svg class="w-3 h-3 inline mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
              </svg>
              Тип товара определяет доступные единицы измерения
            </p>
          </div>

          <!-- Валюта и цена -->
          <div class="grid grid-cols-2 gap-4 mb-4">
            <div>
              <label for="currency" class="block text-sm font-medium text-gray-700 mb-1">
                <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                </svg>
                Валюта
              </label>
              <select
                id="currency"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 bg-white"
                required
              >
                <!-- Динамически заполняется JavaScript -->
              </select>
              <p class="text-xs text-gray-500 mt-1">По умолчанию: Основная валюта</p>
            </div>
            <div>
              <label for="price" class="block text-sm font-medium text-gray-700 mb-1">
                <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                </svg>
                Цена
              </label>
              <input
                type="number"
                id="price"
                placeholder="0.00"
                step="0.01"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                required
              />
            </div>
          </div>

          <!-- Единица измерения -->
          <div class="mb-4 p-4 bg-amber-50 border border-amber-200 rounded-lg">
            <label for="unit" class="block text-sm font-medium text-gray-700 mb-1">
              <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
              </svg>
              Единица измерения
            </label>
            <select
              id="unit"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 bg-white"
              required
            >
              <option value="">Сначала выберите тип товара</option>
            </select>
            <p class="text-xs text-amber-600 mt-1">
              <svg class="w-3 h-3 inline mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
              </svg>
              Доступные единицы зависят от выбранного типа товара
            </p>
          </div>



          <!-- Компонент выбора атрибутов -->
          <div id="attributes-section" class="mb-4">
            <h3 class="text-lg font-medium text-gray-900 mb-3">Атрибуты товара</h3>
            <div id="attributes-container" class="space-y-4">
              <!-- Атрибуты будут добавлены динамически -->
            </div>
            <button
              type="button"
              id="add-attribute-btn"
              class="mt-3 inline-flex items-center px-3 py-2 border border-blue-300 text-sm font-medium rounded text-blue-700 bg-blue-50 hover:bg-blue-100 transition-colors"
            >
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
              Добавить атрибут
            </button>
          </div>

          <div class="mb-4">
            <label for="inStock" class="flex items-center">
              <input
                type="checkbox"
                id="inStock"
                class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                checked
              />
              <span class="ml-2 text-sm text-gray-700">Опубликован (доступен для просмотра на сайте)</span>
            </label>
          </div>
        </div>
      </div>

      <!-- Изображения -->
      <div class="mt-6">
        <h2 class="text-xl font-semibold mb-4">Изображения</h2>

        <div class="mb-4 p-4 bg-blue-50 border border-blue-200 rounded">
          <h3 class="text-sm font-medium text-blue-800 mb-2">Правила именования изображений:</h3>
          <ul class="text-xs text-blue-700 space-y-1">
            <li>• Главное изображение: будет названо как <code>название-товара_main.jpg</code></li>
            <li>• Дополнительные: будут названы как <code>название-товара_1.jpg</code>, <code>название-товара_2.jpg</code> и т.д.</li>
            <li>• Поддерживаются форматы: JPG, PNG</li>
          </ul>
        </div>

        <div class="mb-4">
          <label for="main-image" class="block text-sm font-medium text-gray-700 mb-1">Главное изображение</label>
          <input
            type="file"
            id="main-image"
            accept="image/*"
            class="w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
          />
          <div id="main-image-preview" class="mt-2 hidden">
            <img id="main-image-preview-img" src="" alt="Предпросмотр главного изображения" class="h-20 w-20 object-cover rounded border">
          </div>
        </div>

        <div class="mb-4">
          <label for="additional-images" class="block text-sm font-medium text-gray-700 mb-1">Дополнительные изображения (до 6 штук)</label>
          <input
            type="file"
            id="additional-images"
            accept="image/*"
            multiple
            class="w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
          />
          <div id="additional-images-preview" class="mt-2 hidden">
            <div class="grid grid-cols-6 gap-2" id="additional-images-grid"></div>
          </div>
            <!-- Предпросмотр дополнительных изображений будет добавлен динамически -->
          </div>
        </div>
      </div>

      <div class="mt-8 flex justify-end">
        <a href="/admin/products" class="bg-gray-300 text-gray-800 px-4 py-2 rounded mr-2 hover:bg-gray-400">
          Отмена
        </a>
        <button type="submit" class="create-product-btn text-white px-4 py-2 rounded" style="background-color: #3b82f6;">
          Создать товар
        </button>
      </div>
    </form>
  </div>
</AdminLayout>

<style>
  /* Стили для кнопки создания продукта */
  .create-product-btn:hover {
    background-color: #2563eb !important;
  }

  /* Стили для кнопки генерации ID */
  .generate-id-btn:hover {
    background-color: #2563eb !important;
  }

  /* Стили для кнопки генерации SLUG */
  .generate-product-slug-btn:hover {
    background-color: #2563eb !important;
  }
</style>

<script define:vars={{ attributesData, attributeTypes, settingsData }} is:inline>
  // Данные атрибутов
  window.attributesData = attributesData;
  window.attributeTypes = attributeTypes;

  // Настройки товаров
  window.settingsData = settingsData;

  // Обработчик отправки формы
  document.getElementById('product-form').addEventListener('submit', async (e) => {
    e.preventDefault();

    // Сбор данных формы
    const id = document.getElementById('id').value;
    const name = document.getElementById('name').value;
    const slug = document.getElementById('product-slug').value;
    const category = document.getElementById('category').value;
    const subcategory = document.getElementById('subcategory').value;
    const shortDescription = document.getElementById('shortDescription').value;
    const fullDescription = document.getElementById('fullDescription').value;
    const productType = document.getElementById('product-type').value;
    const currency = document.getElementById('currency').value;
    const price = parseFloat(document.getElementById('price').value);
    const unit = document.getElementById('unit').value;
    const inStock = document.getElementById('inStock').checked;

    // Валидация новых полей
    if (!productType) {
      await window.adminModal?.showError('Пожалуйста, выберите тип товара');
      return;
    }
    if (!currency) {
      await window.adminModal?.showError('Пожалуйста, выберите валюту');
      return;
    }
    if (!unit) {
      await window.adminModal?.showError('Пожалуйста, выберите единицу измерения');
      return;
    }

    // Собираем данные атрибутов из нового компонента
    const selectedAttributes = collectAttributesData();

    // Получение правильного categorySlug из данных категорий
    // Загружаем данные категорий для получения правильного slug
    let categorySlug = category.toLowerCase().replace(/\s+/g, '-');
    try {
      const categoriesResponse = await fetch('/data/product/categories.json');
      const categoriesData = await categoriesResponse.json();
      const foundCategory = categoriesData.categories.find(cat => cat.name === category);
      if (foundCategory) {
        categorySlug = foundCategory.slug;
      }
    } catch (error) {
      console.warn('Не удалось загрузить данные категорий, используется fallback slug:', error);
    }

    // Формирование объекта товара с атрибутами по умолчанию
    const defaultAttributes = {
      colors: [],
      color_pigments: {
        id: "no_pigment",
        name: "без красителя",
        description: "Изделие без добавления цветных пигментов, стандартный серый цвет."
      },
      texture: "",
      size: {
        length: 0,
        width: 0,
        height: 0
      },
      weight: 0,
      strength: "",
      surface: "одноуровневая",
      pattern: "нет рисунка"
    };

    // Объединяем атрибуты по умолчанию с выбранными
    const finalAttributes = { ...defaultAttributes, ...selectedAttributes };

    const productData = {
      id,
      name,
      slug,
      category,
      categorySlug,
      subcategory,
      shortDescription,
      fullDescription,
      productType,
      price: {
        value: price,
        currency,
        unit
      },
      attributes: finalAttributes,
      images: {
        main: `${id}/${name.toLowerCase().replace(/[^а-яё\w\s]/gi, '').replace(/\s+/g, '-').substring(0, 30)}_main.jpg`,
        additional: []
      },
      inStock,
      popularity: 4.0
    };

    try {
      // Сначала создаем товар
      const response = await fetch('/api/admin/products', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(productData)
      });

      if (response.ok) {
        // Загружаем изображения, если они выбраны
        await uploadProductImages(id, name);

        await window.adminModal?.showSuccess('Товар успешно создан!');
        window.location.href = '/admin/products';
      } else {
        const error = await response.json();
        await window.adminModal?.showError('Ошибка при создании товара: ' + (error.error || 'Неизвестная ошибка'));
      }
    } catch (error) {
      console.error('Ошибка:', error);
      await window.adminModal?.showError('Произошла ошибка при создании товара. Проверьте подключение к интернету и попробуйте еще раз.');
    }
  });

  // Функция для загрузки изображений товара
  async function uploadProductImages(productId, productName) {
    const mainImageInput = document.getElementById('main-image');
    const additionalImagesInput = document.getElementById('additional-images');

    // Загружаем главное изображение
    if (mainImageInput.files && mainImageInput.files[0]) {
      await uploadSingleImage(mainImageInput.files[0], productId, productName, true);
    }

    // Загружаем дополнительные изображения
    if (additionalImagesInput.files) {
      for (let i = 0; i < Math.min(additionalImagesInput.files.length, 6); i++) {
        await uploadSingleImage(additionalImagesInput.files[i], productId, productName, false);
      }
    }
  }

  // Функция для загрузки одного изображения
  async function uploadSingleImage(file, productId, productName, isMain) {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('productId', productId);
    formData.append('imageType', isMain ? 'main' : 'additional');

    try {
      const response = await fetch('/api/admin/upload-image', {
        method: 'POST',
        body: formData
      });

      const result = await response.json();

      if (!result.success) {
        console.error('Ошибка при загрузке изображения:', result.error);
      } else {
        console.log('Изображение успешно загружено:', result.imagePath);
      }
    } catch (error) {
      console.error('Ошибка при загрузке изображения:', error);
    }
  }

  // Предпросмотр главного изображения
  document.getElementById('main-image').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = function(e) {
        const preview = document.getElementById('main-image-preview');
        const img = document.getElementById('main-image-preview-img');
        img.src = e.target.result;
        preview.classList.remove('hidden');
      };
      reader.readAsDataURL(file);
    }
  });

  // Предпросмотр дополнительных изображений
  document.getElementById('additional-images').addEventListener('change', function(e) {
    const files = Array.from(e.target.files).slice(0, 6); // Максимум 6 изображений
    const preview = document.getElementById('additional-images-preview');
    const grid = document.getElementById('additional-images-grid');

    // Очищаем предыдущий предпросмотр
    grid.innerHTML = '';

    if (files.length > 0) {
      preview.classList.remove('hidden');

      files.forEach((file, index) => {
        const reader = new FileReader();
        reader.onload = function(e) {
          const imgContainer = document.createElement('div');
          imgContainer.className = 'relative';

          const img = document.createElement('img');
          img.src = e.target.result;
          img.alt = `Дополнительное изображение ${index + 1}`;
          img.className = 'h-16 w-16 object-cover rounded border';

          imgContainer.appendChild(img);
          grid.appendChild(imgContainer);
        };
        reader.readAsDataURL(file);
      });
    } else {
      preview.classList.add('hidden');
    }
  });

  // Автоматическая генерация ID товара при выборе категории
  document.getElementById('category').addEventListener('change', async function(e) {
    const selectedCategory = e.target.value;
    const selectedOption = e.target.selectedOptions[0];
    const categoryId = selectedOption ? selectedOption.getAttribute('data-category-id') : null;

    const idInput = document.getElementById('id');
    const generateBtn = document.getElementById('generate-id-btn');
    const categoryInfo = document.getElementById('category-info');
    const categoryIdPrefix = document.getElementById('category-id-prefix');
    const subcategorySelect = document.getElementById('subcategory');

    if (selectedCategory && categoryId) {
      // Показываем информацию о категории
      categoryIdPrefix.textContent = categoryId;
      categoryInfo.classList.remove('hidden');

      // Включаем кнопку генерации
      generateBtn.disabled = false;

      // Автоматически генерируем ID
      await generateProductId(selectedCategory);

      // Загружаем подкатегории для выбранной категории
      await loadSubcategories(selectedCategory);
    } else {
      // Скрываем информацию и очищаем поля
      categoryInfo.classList.add('hidden');
      generateBtn.disabled = true;
      idInput.value = '';

      // Очищаем и отключаем выбор подкатегории
      subcategorySelect.innerHTML = '<option value="">Сначала выберите категорию</option>';
      subcategorySelect.disabled = true;
    }
  });

  // Обработчик кнопки генерации ID
  document.getElementById('generate-id-btn').addEventListener('click', async function() {
    const category = document.getElementById('category').value;
    if (category) {
      await generateProductId(category);
    }
  });

  // Функция генерации ID товара
  async function generateProductId(categoryName) {
    const idInput = document.getElementById('id');
    const generateBtn = document.getElementById('generate-id-btn');

    try {
      // Показываем состояние загрузки
      generateBtn.textContent = 'Генерация...';
      generateBtn.disabled = true;
      idInput.value = 'Генерируется...';

      const response = await fetch(`/api/admin/generate-product-id?category=${encodeURIComponent(categoryName)}`);
      const result = await response.json();

      if (result.success) {
        idInput.value = result.productId;
        console.log('Сгенерирован ID товара:', result.productId);
      } else {
        alert('Ошибка при генерации ID: ' + (result.error || 'Неизвестная ошибка'));
        idInput.value = '';
      }
    } catch (error) {
      console.error('Ошибка при генерации ID товара:', error);
      alert('Ошибка при генерации ID товара');
      idInput.value = '';
    } finally {
      // Восстанавливаем состояние кнопки
      generateBtn.textContent = 'Генерировать';
      generateBtn.disabled = false;
    }
  }

  // Функция генерации SLUG с транслитерацией
  function generateSlugFromName(name) {
    if (!name) return '';

    // Маппинг кириллических символов в латинские для SLUG
    const cyrillicToLatin = {
      'А': 'a', 'Б': 'b', 'В': 'v', 'Г': 'g', 'Д': 'd', 'Е': 'e', 'Ё': 'yo', 'Ж': 'zh',
      'З': 'z', 'И': 'i', 'Й': 'y', 'К': 'k', 'Л': 'l', 'М': 'm', 'Н': 'n', 'О': 'o',
      'П': 'p', 'Р': 'r', 'С': 's', 'Т': 't', 'У': 'u', 'Ф': 'f', 'Х': 'h', 'Ц': 'ts',
      'Ч': 'ch', 'Ш': 'sh', 'Щ': 'sch', 'Ъ': '', 'Ы': 'y', 'Ь': '', 'Э': 'e', 'Ю': 'yu', 'Я': 'ya',
      'а': 'a', 'б': 'b', 'в': 'v', 'г': 'g', 'д': 'd', 'е': 'e', 'ё': 'yo', 'ж': 'zh',
      'з': 'z', 'и': 'i', 'й': 'y', 'к': 'k', 'л': 'l', 'м': 'm', 'н': 'n', 'о': 'o',
      'п': 'p', 'р': 'r', 'с': 's', 'т': 't', 'у': 'u', 'ф': 'f', 'х': 'h', 'ц': 'ts',
      'ч': 'ch', 'ш': 'sh', 'щ': 'sch', 'ъ': '', 'ы': 'y', 'ь': '', 'э': 'e', 'ю': 'yu', 'я': 'ya'
    };

    // Транслитерация кириллицы в латиницу
    let transliterated = '';
    for (let i = 0; i < name.length; i++) {
      const char = name[i];
      transliterated += cyrillicToLatin[char] || char;
    }

    // Создание SLUG: приведение к нижнему регистру, замена пробелов на дефисы, удаление недопустимых символов
    return transliterated
      .toLowerCase()
      .replace(/\s+/g, '-')           // Заменяем пробелы на дефисы
      .replace(/[^a-z0-9-]/g, '')     // Удаляем все символы кроме букв, цифр и дефисов
      .replace(/--+/g, '-')           // Заменяем множественные дефисы на одинарные
      .replace(/^-+/g, '')            // Удаляем дефисы в начале
      .replace(/-+$/g, '');           // Удаляем дефисы в конце
  }

  // Автоматическая генерация SLUG при вводе названия товара
  document.getElementById('name').addEventListener('input', function() {
    const productSlugInput = document.getElementById('product-slug');
    if (!productSlugInput.value) { // Генерируем только если поле пустое
      const slug = generateSlugFromName(this.value);
      productSlugInput.value = slug;
    }
  });

  // Обработчик кнопки генерации SLUG
  document.getElementById('generate-product-slug-btn').addEventListener('click', function() {
    const nameInput = document.getElementById('name');
    const productSlugInput = document.getElementById('product-slug');

    if (nameInput.value) {
      const slug = generateSlugFromName(nameInput.value);
      productSlugInput.value = slug;
    }
  });

  // Функция для загрузки подкатегорий
  async function loadSubcategories(categoryName) {
    const subcategorySelect = document.getElementById('subcategory');

    try {
      // Загружаем данные категорий
      const response = await fetch('/data/product/categories.json');
      const categoriesData = await response.json();

      // Находим выбранную категорию
      const selectedCategory = categoriesData.categories.find(cat => cat.name === categoryName);

      if (selectedCategory && selectedCategory.subcategories) {
        // Очищаем текущие опции
        subcategorySelect.innerHTML = '<option value="">Выберите подкатегорию</option>';

        // Добавляем подкатегории
        selectedCategory.subcategories.forEach(subcategory => {
          const option = document.createElement('option');
          option.value = subcategory;
          option.textContent = subcategory;
          subcategorySelect.appendChild(option);
        });

        // Включаем выбор подкатегории
        subcategorySelect.disabled = false;
      } else {
        // Если подкатегорий нет
        subcategorySelect.innerHTML = '<option value="">Подкатегории отсутствуют</option>';
        subcategorySelect.disabled = true;
      }
    } catch (error) {
      console.error('Ошибка при загрузке подкатегорий:', error);
      subcategorySelect.innerHTML = '<option value="">Ошибка загрузки подкатегорий</option>';
      subcategorySelect.disabled = true;
    }
  }

  // Управление атрибутами
  let attributeCounter = 0;

  // Инициализация компонента атрибутов
  document.addEventListener('DOMContentLoaded', function() {
    initializeAttributesComponent();
    initializeProductSettings();
  });

  function initializeAttributesComponent() {
    const addAttributeBtn = document.getElementById('add-attribute-btn');
    if (addAttributeBtn) {
      addAttributeBtn.addEventListener('click', addAttributeRow);
    }
  }

  // Инициализация настроек товаров
  function initializeProductSettings() {
    initializeProductTypes();
    initializeCurrencies();
    initializeUnits();
  }

  // Инициализация типов товаров
  function initializeProductTypes() {
    const productTypeSelect = document.getElementById('product-type');
    if (!productTypeSelect || !window.settingsData) return;

    // Заполняем селект типов товаров
    productTypeSelect.innerHTML = '';
    window.settingsData.product_types.supported.forEach(type => {
      const option = document.createElement('option');
      option.value = type.key;
      option.textContent = `${type.label.ru} - ${type.description.ru}`;
      option.selected = type.key === window.settingsData.product_types.primary;
      productTypeSelect.appendChild(option);
    });

    // Обработчик изменения типа товара
    productTypeSelect.addEventListener('change', function() {
      updateAvailableUnits(this.value);
    });

    // Инициализируем единицы для выбранного по умолчанию типа
    if (productTypeSelect.value) {
      updateAvailableUnits(productTypeSelect.value);
    }
  }

  // Инициализация валют
  function initializeCurrencies() {
    const currencySelect = document.getElementById('currency');
    if (!currencySelect || !window.settingsData) return;

    // Заполняем селект валют
    currencySelect.innerHTML = '';
    window.settingsData.currencies.supported.forEach(currency => {
      const option = document.createElement('option');
      option.value = currency.key;
      option.textContent = `${currency.key} - ${currency.label.ru}`;
      option.selected = currency.key === window.settingsData.currencies.primary;
      currencySelect.appendChild(option);
    });
  }

  // Инициализация единиц измерения
  function initializeUnits() {
    const unitSelect = document.getElementById('unit');
    if (!unitSelect) return;

    // Изначально единицы недоступны до выбора типа товара
    unitSelect.innerHTML = '<option value="">Сначала выберите тип товара</option>';
    unitSelect.disabled = true;
  }

  // Обновление доступных единиц измерения в зависимости от типа товара
  function updateAvailableUnits(productType) {
    const unitSelect = document.getElementById('unit');
    if (!unitSelect || !window.settingsData) return;

    // Определяем какие единицы измерения подходят для данного типа товара
    let relevantUnitTypes = [];

    switch (productType) {
      case 'physical':
        relevantUnitTypes = ['weight', 'volume', 'dimensions', 'countable'];
        break;
      case 'digital':
        relevantUnitTypes = ['countable'];
        break;
      case 'service':
        relevantUnitTypes = ['service'];
        break;
      default:
        relevantUnitTypes = ['weight', 'volume', 'dimensions', 'countable', 'service'];
    }

    // Очищаем селект
    unitSelect.innerHTML = '';
    unitSelect.disabled = false;

    // Добавляем опцию по умолчанию
    const defaultOption = document.createElement('option');
    defaultOption.value = '';
    defaultOption.textContent = 'Выберите единицу измерения';
    unitSelect.appendChild(defaultOption);

    // Добавляем единицы измерения для каждого подходящего типа
    relevantUnitTypes.forEach(unitType => {
      if (window.settingsData.units[unitType] && window.settingsData.units[unitType].supported) {
        // Добавляем заголовок группы
        const optgroup = document.createElement('optgroup');
        optgroup.label = getUnitTypeLabel(unitType);

        window.settingsData.units[unitType].supported.forEach(unit => {
          const option = document.createElement('option');
          option.value = unit.key;
          option.textContent = `${unit.key} - ${unit.label.ru}`;

          // Выбираем основную единицу по умолчанию для первого типа
          if (unitType === relevantUnitTypes[0] && unit.key === window.settingsData.units[unitType].primary) {
            option.selected = true;
          }

          optgroup.appendChild(option);
        });

        unitSelect.appendChild(optgroup);
      }
    });
  }

  // Получить название типа единицы измерения
  function getUnitTypeLabel(unitType) {
    const labels = {
      'weight': 'Вес',
      'volume': 'Объем',
      'dimensions': 'Размеры',
      'countable': 'Штучные',
      'service': 'Услуги'
    };
    return labels[unitType] || unitType;
  }

  function addAttributeRow() {
    const container = document.getElementById('attributes-container');
    if (!container) return;

    // Сворачиваем все существующие атрибуты перед добавлением нового
    collapseAllAttributes();

    const attributeId = `attribute-${++attributeCounter}`;
    const attributeRow = document.createElement('div');
    attributeRow.className = 'attribute-row bg-gray-50 p-4 rounded-lg border';
    attributeRow.id = attributeId;

    attributeRow.innerHTML = `
      <div class="attribute-header grid grid-cols-1 md:grid-cols-12 gap-4">
        <div class="md:col-span-4">
          <label class="block text-sm font-medium text-gray-700 mb-1">Тип атрибута</label>
          <select class="attribute-type-select w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" data-attribute-id="${attributeId}">
            <option value="">Выберите тип атрибута</option>
            ${Object.entries(window.attributeTypes).map(([key, config]) =>
              `<option value="${key}">${config.name}</option>`
            ).join('')}
          </select>
        </div>
        <div class="attribute-value-container md:col-span-6">
          <label class="block text-sm font-medium text-gray-700 mb-1">Значение</label>
          <div class="attribute-value-content">
            <p class="text-sm text-gray-500">Сначала выберите тип атрибута</p>
          </div>
        </div>
        <div class="md:col-span-2 flex items-end justify-end">
          <button type="button" class="remove-attribute-btn px-3 py-2 bg-red-100 hover:bg-red-200 text-red-700 border border-red-300 rounded text-sm" data-attribute-id="${attributeId}">
            удалить
          </button>
        </div>
      </div>
    `;

    container.appendChild(attributeRow);

    // Добавляем обработчики событий
    const typeSelect = attributeRow.querySelector('.attribute-type-select');
    const removeBtn = attributeRow.querySelector('.remove-attribute-btn');

    if (typeSelect) {
      typeSelect.addEventListener('change', function() {
        handleAttributeTypeChange(this);
      });

      // Добавляем обработчик для сворачивания других атрибутов при фокусе
      typeSelect.addEventListener('focus', function() {
        collapseOtherAttributes(attributeId);
      });
    }

    if (removeBtn) {
      removeBtn.addEventListener('click', function() {
        removeAttributeRow(this.dataset.attributeId);
      });
    }

    // Добавляем обработчик для сворачивания при клике на значения
    const valueContainer = attributeRow.querySelector('.attribute-value-container');
    if (valueContainer) {
      valueContainer.addEventListener('click', function() {
        collapseOtherAttributes(attributeId);
      });
    }
  }

  function handleAttributeTypeChange(selectElement) {
    const attributeType = selectElement.value;
    const attributeId = selectElement.dataset.attributeId;
    const row = document.getElementById(attributeId);

    if (!row || !attributeType) return;

    const valueContainer = row.querySelector('.attribute-value-content');
    if (!valueContainer) return;

    const typeConfig = window.attributeTypes[attributeType];
    const attributeData = window.attributesData[attributeType];

    if (!typeConfig || !attributeData) {
      valueContainer.innerHTML = '<p class="text-sm text-red-500">Ошибка загрузки данных атрибута</p>';
      return;
    }

    // Генерируем интерфейс в зависимости от типа атрибута
    if (typeConfig.isSimpleArray) {
      // Простой массив строк (например, текстуры)
      valueContainer.innerHTML = `
        <select class="attribute-value-select w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" multiple>
          ${attributeData.map(item => `<option value="${item}">${item}</option>`).join('')}
        </select>
        <p class="text-xs text-gray-500 mt-1">Удерживайте Ctrl для выбора нескольких значений</p>
      `;
    } else if (attributeType === 'colors') {
      // Специальная обработка для цветов
      valueContainer.innerHTML = `
        <div class="space-y-2">
          ${attributeData.map(color => `
            <label class="flex items-center">
              <input type="checkbox" class="attribute-checkbox mr-3" value="${color.id}" data-name="${color.name}">
              <div class="w-5 h-5 rounded border mr-3" style="background-color: ${color.hex}"></div>
              <span class="text-sm font-semibold">${color.name}</span>
            </label>
          `).join('')}
        </div>
      `;
    } else if (attributeType === 'sizes') {
      // Специальная обработка для размеров
      valueContainer.innerHTML = `
        <div class="space-y-2">
          ${Object.entries(attributeData).map(([category, sizes]) => `
            <div>
              <h4 class="text-sm font-medium text-gray-700 mb-1">${category}</h4>
              ${sizes.map((size, index) => `
                <label class="flex items-center mb-1">
                  <input type="radio" class="attribute-radio mr-3" name="size-${attributeId}" value="${JSON.stringify(size).replace(/"/g, '&quot;')}" data-category="${category}">
                  <span class="text-sm font-semibold">${size.length}×${size.width}×${size.height} мм</span>
                </label>
              `).join('')}
            </div>
          `).join('')}
        </div>
      `;
    } else if (['strength_classes', 'frost_resistance', 'water_absorption'].includes(attributeType)) {
      // Атрибуты с классом и описанием
      valueContainer.innerHTML = `
        <select class="attribute-value-select w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
          <option value="">Выберите значение</option>
          ${attributeData.map(item => `<option value="${item.class}" data-description="${item.description}">${item.class} - ${item.description}</option>`).join('')}
        </select>
      `;
    } else if (attributeType === 'weight') {
      // Специальная обработка для веса
      valueContainer.innerHTML = `
        <select class="attribute-value-select w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
          <option value="">Выберите вес</option>
          ${attributeData.map(item => `<option value="${JSON.stringify(item).replace(/"/g, '&quot;')}">${item.value} ${item.unit}</option>`).join('')}
        </select>
      `;
    } else if (['surfaces', 'patterns', 'color_pigments'].includes(attributeType)) {
      // Атрибуты с id, name и description
      valueContainer.innerHTML = `
        <select class="attribute-value-select w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
          <option value="">Выберите значение</option>
          ${attributeData.map(item => `<option value="${item.id}" data-name="${item.name}" data-description="${item.description}">${item.name}</option>`).join('')}
        </select>
      `;
    } else {
      // Обычный список
      valueContainer.innerHTML = `
        <select class="attribute-value-select w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
          <option value="">Выберите значение</option>
          ${attributeData.map(item => `<option value="${typeof item === 'string' ? item : item.id || item.name}">${typeof item === 'string' ? item : item.name || item.id}</option>`).join('')}
        </select>
      `;
    }
  }

  function removeAttributeRow(attributeId) {
    const row = document.getElementById(attributeId);
    if (row) {
      row.remove();
    }
  }

  // Функция для сворачивания всех атрибутов
  function collapseAllAttributes() {
    const allAttributeRows = document.querySelectorAll('.attribute-row');

    allAttributeRows.forEach(row => {
      const typeSelect = row.querySelector('.attribute-type-select');
      const valueContainer = row.querySelector('.attribute-value-content');

      // Проверяем, есть ли выбранный тип и значение
      if (typeSelect && typeSelect.value && valueContainer) {
        const hasSelectedValue = checkIfHasSelectedValue(row, typeSelect.value);

        if (hasSelectedValue) {
          collapseAttributeRow(row, typeSelect.value);
        }
      }
    });
  }

  // Функция для сворачивания других атрибутов
  function collapseOtherAttributes(currentAttributeId) {
    const allAttributeRows = document.querySelectorAll('.attribute-row');

    allAttributeRows.forEach(row => {
      if (row.id !== currentAttributeId) {
        const typeSelect = row.querySelector('.attribute-type-select');
        const valueContainer = row.querySelector('.attribute-value-content');

        // Проверяем, есть ли выбранный тип и значение
        if (typeSelect && typeSelect.value && valueContainer) {
          const hasSelectedValue = checkIfHasSelectedValue(row, typeSelect.value);

          if (hasSelectedValue) {
            collapseAttributeRow(row, typeSelect.value);
          }
        }
      }
    });
  }

  // Проверяем, есть ли выбранное значение в атрибуте
  function checkIfHasSelectedValue(row, attributeType) {
    if (attributeType === 'colors') {
      return row.querySelectorAll('.attribute-checkbox:checked').length > 0;
    } else if (attributeType === 'standard_sizes') {
      return row.querySelector('.attribute-radio:checked') !== null;
    } else {
      const select = row.querySelector('.attribute-value-select');
      return select && select.value;
    }
  }

  // Сворачиваем конкретную строку атрибута
  function collapseAttributeRow(row, attributeType) {
    const valueContainer = row.querySelector('.attribute-value-content');
    if (!valueContainer) return;

    // Получаем выбранные значения для отображения
    const selectedValues = getSelectedValuesText(row, attributeType);

    if (selectedValues) {
      valueContainer.innerHTML = `
        <div class="collapsed-view p-3 bg-gray-100 rounded border cursor-pointer" onclick="expandAttributeRow('${row.id}')">
          <div class="text-sm font-semibold text-gray-800">${selectedValues}</div>
          <div class="text-xs text-gray-500 mt-1">Нажмите для редактирования</div>
        </div>
      `;

      // Добавляем класс для отслеживания состояния
      row.classList.add('collapsed');
    }
  }

  // Получаем текст выбранных значений
  function getSelectedValuesText(row, attributeType) {
    if (attributeType === 'colors') {
      const checked = row.querySelectorAll('.attribute-checkbox:checked');
      return Array.from(checked).map(cb => cb.dataset.name).join(', ');
    } else if (attributeType === 'standard_sizes') {
      const selected = row.querySelector('.attribute-radio:checked');
      if (selected) {
        const size = JSON.parse(selected.value.replace(/&quot;/g, '"'));
        return `${size.length}×${size.width}×${size.height} мм`;
      }
    } else if (attributeType === 'weight') {
      const select = row.querySelector('.attribute-value-select');
      if (select && select.value) {
        const weight = JSON.parse(select.value.replace(/&quot;/g, '"'));
        return `${weight.value} ${weight.unit}`;
      }
    } else {
      const select = row.querySelector('.attribute-value-select');
      if (select && select.value) {
        const selectedOption = select.selectedOptions[0];
        return selectedOption.textContent;
      }
    }
    return null;
  }

  // Разворачиваем атрибут (глобальная функция для onclick)
  window.expandAttributeRow = function(attributeId) {
    const row = document.getElementById(attributeId);
    if (!row) return;

    const typeSelect = row.querySelector('.attribute-type-select');
    if (typeSelect && typeSelect.value) {
      // Восстанавливаем полный интерфейс
      handleAttributeTypeChange(typeSelect);
      row.classList.remove('collapsed');

      // Сворачиваем другие атрибуты
      collapseOtherAttributes(attributeId);
    }
  };

  // Функция для сбора данных атрибутов
  function collectAttributesData() {
    const attributes = {};
    const attributeRows = document.querySelectorAll('.attribute-row');

    attributeRows.forEach(row => {
      const typeSelect = row.querySelector('.attribute-type-select');
      const attributeType = typeSelect?.value;

      if (!attributeType) return;

      if (attributeType === 'colors') {
        const checkboxes = row.querySelectorAll('.attribute-checkbox:checked');
        attributes.colors = Array.from(checkboxes).map(cb => cb.dataset.name);
      } else if (attributeType === 'standard_sizes') {
        const selectedRadio = row.querySelector('.attribute-radio:checked');
        if (selectedRadio) {
          attributes.size = JSON.parse(selectedRadio.value.replace(/&quot;/g, '"'));
        }
      } else if (attributeType === 'color_pigments') {
        const select = row.querySelector('.attribute-value-select');
        const selectedOption = select?.selectedOptions[0];
        if (selectedOption && selectedOption.value) {
          attributes.color_pigments = {
            id: selectedOption.value,
            name: selectedOption.dataset.name,
            description: selectedOption.dataset.description
          };
        }
      } else if (attributeType === 'surfaces') {
        const select = row.querySelector('.attribute-value-select');
        if (select?.value) {
          attributes.surface = select.value;
        }
      } else if (attributeType === 'patterns') {
        const select = row.querySelector('.attribute-value-select');
        if (select?.value) {
          attributes.pattern = select.value;
        }
      } else if (attributeType === 'strength_classes') {
        const select = row.querySelector('.attribute-value-select');
        if (select?.value) {
          attributes.strength = select.value;
        }
      } else if (attributeType === 'frost_resistance') {
        const select = row.querySelector('.attribute-value-select');
        if (select?.value) {
          attributes.frost_resistance = select.value;
        }
      } else if (attributeType === 'water_absorption') {
        const select = row.querySelector('.attribute-value-select');
        if (select?.value) {
          attributes.water_absorption = select.value;
        }
      } else if (attributeType === 'textures') {
        const select = row.querySelector('.attribute-value-select');
        if (select && select.multiple) {
          const selectedOptions = Array.from(select.selectedOptions);
          if (selectedOptions.length > 0) {
            attributes.texture = selectedOptions[0].value; // берем первое значение для совместимости
          }
        } else if (select?.value) {
          attributes.texture = select.value;
        }
      } else if (attributeType === 'weight') {
        const select = row.querySelector('.attribute-value-select');
        if (select?.value) {
          const weightData = JSON.parse(select.value.replace(/&quot;/g, '"'));
          attributes.weight = weightData.value;
          attributes.weight_unit = weightData.unit;
        }
      } else {
        // Обычные атрибуты
        const select = row.querySelector('.attribute-value-select');
        if (select) {
          if (select.multiple) {
            const selectedOptions = Array.from(select.selectedOptions);
            attributes[attributeType] = selectedOptions.map(option => option.value);
          } else if (select.value) {
            attributes[attributeType] = select.value;
          }
        }
      }
    });

    return attributes;
  }
</script>
