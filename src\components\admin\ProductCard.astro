---
import Badge from '../ui/Badge.astro';

export interface Product {
  id: string;
  name: string;
  category: string;
  popularity: number;
  basePrice: {
    value: number;
    unit: string;
  };
  inStock: boolean;
}

export interface Props {
  product: Product;
  class?: string;
}

const { product, class: className = '' } = Astro.props;
---

<div class={`bg-white rounded-lg border border-gray-200 p-4 shadow-sm hover:shadow-md transition-shadow ${className}`}>
  <div class="flex items-start justify-between mb-3">
    <div class="flex items-center">
      <div class="h-10 w-10 flex-shrink-0">
        <div class="h-10 w-10 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
          <svg class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z" />
          </svg>
        </div>
      </div>
      <div class="ml-3">
        <h3 class="text-sm font-medium text-gray-900">{product.name}</h3>
        <p class="text-xs text-gray-500">{product.id}</p>
      </div>
    </div>
    <Badge variant={product.inStock ? 'success' : 'destructive'}>
      {product.inStock ? 'В наличии' : 'Нет в наличии'}
    </Badge>
  </div>

  <div class="space-y-2">
    <div class="flex justify-between items-center">
      <span class="text-xs text-gray-500">Категория:</span>
      <Badge variant="outline">{product.category}</Badge>
    </div>

    <div class="flex justify-between items-center">
      <span class="text-xs text-gray-500">Рейтинг:</span>
      <div class="flex items-center">
        <svg class="h-4 w-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
        </svg>
        <span class="ml-1 text-sm font-medium text-gray-900">{product.popularity}</span>
      </div>
    </div>

    <div class="flex justify-between items-center">
      <span class="text-xs text-gray-500">Цена:</span>
      <span class="text-sm font-medium text-gray-900">{product.basePrice.value} ₽/{product.basePrice.unit}</span>
    </div>
  </div>
</div>
